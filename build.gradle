plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.5'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.intfly'
version = '0.0.3-SNAPSHOT'
description = 'photo-admin'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'io.github.wimdeblauwe:htmx-spring-boot-thymeleaf:4.0.1'
    implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    runtimeOnly 'com.mysql:mysql-connector-j'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    runtimeOnly 'org.webjars.npm:tabler__core:1.4.0'
    runtimeOnly 'org.webjars.npm:alpinejs:3.15.0'
    runtimeOnly 'org.webjars.npm:htmx.org:2.0.7'
}

tasks.named('test') {
    useJUnitPlatform()
}
