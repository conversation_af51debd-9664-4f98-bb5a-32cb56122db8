<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <aside th:fragment="sidebar" class="navbar navbar-vertical navbar-expand-lg" data-bs-theme="dark">
        <div class="container-fluid">
            <!-- Logo 和手機版選單按鈕 -->
            <div class="navbar-header d-flex justify-content-between align-items-center">
                <h1 class="navbar-brand navbar-brand-autodark">
                    <a href="/" class="text-white text-decoration-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-camera me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 7h1a2 2 0 0 0 2 -2a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1a2 2 0 0 0 2 2h1a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-9a2 2 0 0 1 2 -2"/>
                            <path d="M9 13a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"/>
                        </svg>
                        相片咖管理
                    </a>
                </h1>

                <!-- 手機版選單切換按鈕 -->
                <button class="navbar-toggler d-lg-none"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#sidebar-menu"
                        aria-controls="sidebar-menu"
                        aria-expanded="false"
                        aria-label="切換導航選單">
                    <span class="navbar-toggler-icon"></span>
                </button>
            </div>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="sidebar-menu">
                <ul class="navbar-nav pt-lg-3">
                    <!-- 活動管理 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#navbar-event" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                            <span class="nav-link-icon d-md-none d-lg-inline-block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z"/>
                                    <path d="M16 3v4"/>
                                    <path d="M8 3v4"/>
                                    <path d="M4 11h16"/>
                                    <path d="M11 15h1"/>
                                    <path d="M12 15v3"/>
                                </svg>
                            </span>
                            <span class="nav-link-title">活動管理</span>
                        </a>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-columns">
                                <div class="dropdown-menu-column">
                                    <a class="dropdown-item" href="/activity">
                                        活動列表
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- 相簿管理 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#navbar-album" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                            <span class="nav-link-icon d-md-none d-lg-inline-block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M15 8h.01"/>
                                    <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"/>
                                    <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"/>
                                    <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"/>
                                </svg>
                            </span>
                            <span class="nav-link-title">相簿管理</span>
                        </a>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-columns">
                                <div class="dropdown-menu-column">
                                    <a class="dropdown-item" href="/albums">
                                        相簿列表
                                    </a>
                                    <a class="dropdown-item" href="/photos">
                                        相片管理
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- 權限管理 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#navbar-permission" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                            <span class="nav-link-icon d-md-none d-lg-inline-block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3"/>
                                    <path d="M9 12l2 2l4 -4"/>
                                </svg>
                            </span>
                            <span class="nav-link-title">權限管理</span>
                        </a>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-columns">
                                <div class="dropdown-menu-column">
                                    <a class="dropdown-item" href="/users">
                                        使用者管理
                                    </a>
                                   <!-- <a class="dropdown-item" >
                                        角色管理
                                    </a>-->
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- 運動咖文章管理 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#navbar-article" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
                            <span class="nav-link-icon d-md-none d-lg-inline-block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
                                    <path d="M9 9l1 0"/>
                                    <path d="M9 13l6 0"/>
                                    <path d="M9 17l6 0"/>
                                </svg>
                            </span>
                            <span class="nav-link-title">運動咖文章管理</span>
                        </a>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-columns">
                                <div class="dropdown-menu-column">
                                    <a class="dropdown-item" href="/articles">
                                        文章列表
                                    </a>
                                    <a class="dropdown-item" href="/articles/tags">
                                        標籤管理
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>

                <!-- 使用者資訊區塊 -->
                <div class="mt-auto pt-3 border-top border-secondary">
                    <div class="navbar-nav">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false">
                                <span class="nav-link-icon d-md-none d-lg-inline-block me-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"/>
                                        <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                    </svg>
                                </span>
                                <div class="d-flex flex-column">
                                    <span class="nav-link-title text-white" th:text="${#authentication.name}">使用者名稱</span>
                                </div>
                            </a>
                            <div class="dropdown-menu">
                                <div class="dropdown-divider"></div>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"/>
                                            <path d="M9 12h12l-3 -3"/>
                                            <path d="M18 15l3 -3"/>
                                        </svg>
                                        登出
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- 手機版選單的額外樣式 -->
    <style>
        /* 手機版選單樣式調整 */
        @media (max-width: 991.98px) {
            .navbar-vertical {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                z-index: 1050;
                background-color: var(--tblr-dark) !important;
            }

            .navbar-header {
                padding: 0.5rem 1rem;
            }

            .navbar-toggler {
                border: none;
                padding: 0.25rem 0.5rem;
                color: rgba(255, 255, 255, 0.75);
            }

            .navbar-toggler:focus {
                box-shadow: none;
            }

            .navbar-toggler-icon {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
                width: 1.5em;
                height: 1.5em;
            }

            .collapse:not(.show) {
                display: none;
            }

            .navbar-collapse {
                background-color: var(--tblr-dark);
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                margin-top: 0.5rem;
            }
        }
    </style>
</body>
</html>