<!DOCTYPE html>
<html lang="zh-TW" xmlns:th="http://www.thymeleaf.org">
<body>
<div th:fragment="pagination(page, size, search, baseUrl)" class="card-footer d-flex align-items-center">
    <p class="text-muted">
        第 <span th:text="${page.number + 1}">1</span> 頁，共 <span th:text="${page.totalPages}">10</span> 頁
        （共 <span th:text="${page.totalElements}">100</span> 筆資料）
    </p>
    <ul class="pagination m-0 ms-auto">
        <!-- 上一頁 -->
        <li class="page-item" th:classappend="${!page.hasPrevious() ? 'disabled' : ''}">
            <a class="page-link"
               th:href="${page.hasPrevious() ? baseUrl + '?page=' + (page.number - 1) + '&size=' + size +  (search != null ? '&search=' + search : '') : '#'}"
               tabindex="-1">上一頁</a>
        </li>

        <!-- 顯示當前頁前後各2頁 -->
        <th:block th:each="pageNum : ${#numbers.sequence(
            T(java.lang.Math).max(0, page.number - 2),
            T(java.lang.Math).min(page.totalPages - 1, page.number + 2)
        )}">
            <li class="page-item" th:classappend="${pageNum == page.number ? 'active' : ''}">
                <a class="page-link"
                   th:href="${baseUrl + '?page=' + pageNum + '&size=' + size + (search != null ? '&search=' + search : '')}"
                   th:text="${pageNum + 1}">1</a>
            </li>
        </th:block>

        <!-- 下一頁 -->
        <li class="page-item" th:classappend="${!page.hasNext() ? 'disabled' : ''}">
            <a class="page-link"
               th:href="${page.hasNext() ? baseUrl + '?page=' + (page.number + 1) + '&size=' + size + (search != null ? '&search=' + search : '') : '#'}">下一頁</a>
        </li>
    </ul>
</div>
</body>
</html>