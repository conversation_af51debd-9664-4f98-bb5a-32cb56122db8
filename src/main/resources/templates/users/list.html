<!DOCTYPE html>
<html  lang="zh-TW"  xmlns:th="http://www.thymeleaf.org">
<head>
    <title>使用者管理 - 相片咖管理</title>
    <th:block th:replace="~{fragments/commonResources :: commonResources}"></th:block>
</head>
<body>
    <div class="page" x-data="userManagement()">
        <!-- Sidebar -->
        <div th:replace="~{fragments/sidebar :: sidebar}"></div>

        <!-- Page wrapper -->
        <div class="page-wrapper">
            <div th:replace="~{fragments/message :: message}"></div>
            <!-- Page header -->
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <h2 class="page-title">使用者管理</h2>
                        </div>
                        <div class="col-auto ms-auto d-print-none">
                            <div class="btn-list">
                                <a href="/users/new" class="btn btn-primary d-none d-sm-inline-block">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                    新增使用者
                                </a>
                                <a href="/users/new" class="btn btn-primary d-sm-none btn-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    <div class="row row-deck row-cards">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">使用者列表</h3>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover table-vcenter card-table">
                                        <thead>
                                            <tr>
                                                <th>使用者名稱</th>
                                                <th>電子郵件</th>
                                                <th>角色</th>
                                                <th>建立時間</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="user : ${userPage.content}">
                                                <td th:text="${user.username}">使用者名稱</td>
                                                <td th:text="${user.email}">電子郵件</td>
                                                <td th:text="${user.role != null ? user.role.name : 'USER'}">角色</td>
                                                <td th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd HH:mm')}">建立時間</td>
                                                <td>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-primary"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#editUserModal"
                                                            th:@click="|openEditModal({
                                                                id: ${user.id},
                                                                username: '${user.username}',
                                                                email: '${user.email}',
                                                                enabled: '${user.enabled}',
                                                                roleName: '${user.role.name}'
                                                            })|">
                                                        修改
                                                    </button>
                                                    <form th:action="@{/users/{id}/delete(id=${user.id})}" method="post" style="display: inline;">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('確定要刪除此使用者嗎？')">刪除</button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="card-footer d-flex align-items-center">
                                    <p class="text-muted">
                                        第 <span th:text="${userPage.number + 1}">1</span> 頁，共 <span th:text="${userPage.totalPages}">10</span> 頁
                                        （共 <span th:text="${userPage.totalElements}">100</span> 筆資料）
                                    </p>
                                    <!-- 使用共用的分頁 fragment -->
                                    <th:block th:replace="~{fragments/pagination :: pagination(${userPage}, ${size}, ${search}, '/users')}"></th:block>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit User Modal -->
        <div class="modal modal-blur fade"
             id="editUserModal"
             role="dialog"
             aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">修改使用者</h5>
                        <button type="button"
                                class="btn-close"
                                data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <!-- 修改 Modal 中的表單 -->
                    <form :action="`/users/${editForm.id}/edit`" method="post">
                        <!-- 加入 CSRF Token -->
                        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>

                        <div class="modal-body">

                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label" for="username">使用者名稱</label>
                                        <input type="text"
                                               class="form-control"
                                               x-model="editForm.username"
                                               readonly
                                               name="username"
                                               id="username">
                                        <small class="form-hint">使用者名稱無法修改</small>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label" for="email">電子郵件 <span class="text-danger">*</span></label>
                                        <input type="email"
                                               class="form-control"
                                               name="email"
                                               id="email"
                                               x-model="editForm.email"
                                               required
                                               placeholder="請輸入電子郵件地址">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label" for="roleName">角色</label>
                                        <select class="form-select" name="roleName"  id="roleName" x-model="editForm.roleName">
                                            <option value="">請選擇角色</option>
                                            <option th:each="role : ${roles}"
                                                    th:value="${role.name}"
                                                    th:text="${role.name}">
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label class="form-label" for="enabled">帳號狀態</label>
                                        <div>
                                            <label class="form-check form-switch">
                                                <input class="form-check-input"
                                                       type="checkbox"
                                                       name="enabled"
                                                       id="enabled"
                                                       value="true"
                                                       x-model="editForm.enabled">
                                                <span class="form-check-label" x-text="editForm.enabled ? '啟用' : '停用'"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="newPassword">新密碼</label>
                                <input type="password"
                                       class="form-control"
                                       name="newPassword"
                                       id="newPassword"
                                       x-model="editForm.newPassword"
                                       placeholder="如不修改密碼請留空">
                                <small class="form-hint">留空表示不修改密碼</small>
                            </div>

                            <div class="mb-3" x-show="editForm.newPassword">
                                <label class="form-label" for="confirmPassword">確認新密碼</label>
                                <input type="password"
                                       class="form-control"
                                       x-model="editForm.confirmPassword"
                                       name="confirmPassword"
                                       id="confirmPassword"
                                       placeholder="請再次輸入新密碼">
                                <div x-show="editForm.newPassword && editForm.confirmPassword && editForm.newPassword !== editForm.confirmPassword"
                                     class="text-danger small mt-1">
                                    密碼不一致
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="#"
                               class="btn btn-link link-secondary"
                               data-bs-dismiss="modal">
                                取消
                            </a>
                            <button type="submit"
                                    class="btn btn-primary ms-auto"
                                    @click="submitEditForm($event)"
                                    :disabled="(editForm.newPassword !== editForm.confirmPassword)">
                                更新使用者
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>



    <script>
        function userManagement() {
            return {
                isSubmitting: false,
                editForm: {
                    id: null,
                    username: '',
                    email: '',
                    enabled: true,
                    roleName: '',
                    newPassword: '',
                    confirmPassword: ''
                },

                openEditModal(user) {
                    this.editForm = {
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        enabled: user.enabled,
                        roleName: user.roleName,
                        newPassword: '',
                        confirmPassword: ''
                    };
                },

                resetForm() {
                    this.editForm = {
                        id: null,
                        username: '',
                        email: '',
                        enabled: true,
                        roleName: '',
                        newPassword: '',
                        confirmPassword: ''
                    };
                },

                submitEditForm(event) {
                    if (this.editForm.newPassword && this.editForm.newPassword !== this.editForm.confirmPassword) {
                        event.preventDefault();
                        this.errorMessage = '密碼不一致，請重新確認！';
                        return false;
                    }
                    return true;
                }
            }
        }
    </script>
</body>
</html>