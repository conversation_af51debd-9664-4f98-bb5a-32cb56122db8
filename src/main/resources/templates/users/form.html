<!DOCTYPE html>
<html lang="zh-TW" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>新增使用者 - Photo Admin</title>
    <th:block th:replace="~{fragments/commonResources :: commonResources}"></th:block>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="page-title">新增使用者</h4>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/users/new}" method="post" th:object="${user}">
                            <div class="mb-3">
                                <label for="username" class="form-label">使用者名稱 <span class="text-danger">*</span></label>
                                <input type="text"
                                       class="form-control"
                                       id="username"
                                       name="username"
                                       th:field="*{username}"
                                       required
                                       placeholder="請輸入使用者名稱">
                                <div class="form-text">使用者名稱必須是唯一的</div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">密碼 <span class="text-danger">*</span></label>
                                <input type="password"
                                       class="form-control"
                                       id="password"
                                       name="password"
                                       required
                                       minlength="6"
                                       placeholder="請輸入密碼">
                                <div class="form-text">密碼至少需要6個字元</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">確認密碼 <span class="text-danger">*</span></label>
                                <input type="password"
                                       class="form-control"
                                       id="confirmPassword"
                                       name="confirmPassword"
                                       required
                                       placeholder="請再次輸入密碼">
                                <div class="invalid-feedback" id="passwordMismatch" style="display: none;">
                                    密碼不一致
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">電子郵件 <span class="text-danger">*</span></label>
                                <input type="email"
                                       class="form-control"
                                       id="email"
                                       name="email"
                                       th:field="*{email}"
                                       required
                                       placeholder="請輸入電子郵件地址">
                                <div class="form-text">電子郵件必須是唯一的</div>
                            </div>

                            <div class="mb-3">
                                <label for="roleName" class="form-label">角色</label>
                                <select class="form-select" id="roleName" name="roleName">
                                    <option value="">請選擇角色</option>
                                    <option th:each="role : ${roles}"
                                            th:value="${role.name}"
                                            th:text="${role.name}">
                                    </option>
                                </select>
                                <div class="form-text">如果不選擇，將預設為 USER 角色</div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/users}" class="btn btn-secondary me-md-2">取消</a>
                                <button type="submit" class="btn btn-primary" id="submitBtn">建立使用者</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script>
        // 密碼確認驗證
        document.addEventListener('DOMContentLoaded', function() {
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirmPassword');
            const passwordMismatch = document.getElementById('passwordMismatch');
            const submitBtn = document.getElementById('submitBtn');

            function validatePasswords() {
                if (password.value !== confirmPassword.value) {
                    confirmPassword.classList.add('is-invalid');
                    passwordMismatch.style.display = 'block';
                    submitBtn.disabled = true;
                } else {
                    confirmPassword.classList.remove('is-invalid');
                    passwordMismatch.style.display = 'none';
                    submitBtn.disabled = false;
                }
            }

            password.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);

            // 表單提交前最終驗證
            document.querySelector('form').addEventListener('submit', function(e) {
                if (password.value !== confirmPassword.value) {
                    e.preventDefault();
                    alert('密碼不一致，請重新確認！');
                }
            });
        });
    </script>
</body>
</html>