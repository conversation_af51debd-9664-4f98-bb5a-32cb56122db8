<!DOCTYPE html>
<html lang="zh-TW" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>相簿管理 - 相簿列表</title>
    <th:block th:replace="~{fragments/commonResources :: commonResources}"></th:block>
</head>
<body>
<div class="page">
    <!-- Sidebar -->
    <div th:replace="~{fragments/sidebar :: sidebar}"></div>

    <!-- Page wrapper -->
    <div class="page-wrapper">
        <div th:replace="~{fragments/message :: message}"></div>
        <!-- Page header -->
        <div class="page-header d-print-none">
            <div class="container-xl">
                <div class="row g-2 align-items-center">
                    <div class="col">
                        <h2 class="page-title">相簿管理</h2>
                    </div>
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <a href="/albums/new" class="btn btn-primary d-none d-sm-inline-block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                新增相簿
                            </a>
                            <a href="/albums/new" class="btn btn-primary d-sm-none btn-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page body -->
        <div class="page-body">
            <div class="container-xl">
                <div class="row row-deck row-cards">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">相簿列表</h3>
                                <div class="card-actions">
                                    <form method="get" class="d-flex">
                                        <input type="search" name="search" th:value="${search}" class="form-control me-2" placeholder="搜尋相簿名稱...">
                                        <button type="submit" class="btn btn-outline-primary">搜尋</button>
                                    </form>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover table-vcenter card-table custom-table">
                                    <thead>
                                    <tr>
                                        <th>相簿編號</th>
                                        <th>相簿名稱</th>
                                        <th>活動名稱</th>
                                        <th>狀態</th>
                                        <th>拍攝期間</th>
                                        <th>建立時間</th>
                                        <th>修改時間</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr th:each="album : ${albumPage.content}">
                                        <td th:text="${album.albumId}">編號</td>
                                        <td th:text="${album.name}">名稱</td>
                                        <td th:text="${album.evActivity != null ? album.evActivity.title : ''}">活動名稱</td>
                                        <td th:text="${album.statusName}"></td>
                                        <td th:text="${album.shootStart != null ? #temporals.format(album.shootStart, 'yyyy-MM-dd') : ''} + ' ~ ' + ${album.shootEnd != null ? #temporals.format(album.shootEnd, 'yyyy-MM-dd') : ''}">拍攝期間</td>
                                        <td th:text="${#temporals.format(album.dateAdded, 'yyyy-MM-dd HH:mm')}">建立時間</td>
                                        <td th:text="${#temporals.format(album.dateModified, 'yyyy-MM-dd HH:mm')}">修改時間</td>
                                        <td>
                                            <form th:action="@{/albums/{id}/edit(id=${album.albumId})}" method="get" style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-info">
                                                    修改
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <th:block th:replace="~{fragments/pagination :: pagination(${albumPage}, ${size}, ${search}, '/albums')}"></th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>