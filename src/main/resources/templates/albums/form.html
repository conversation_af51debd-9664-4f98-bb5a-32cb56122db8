<!DOCTYPE html>
<html lang="zh-TW" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>相簿管理 - 相簿表單</title>
    <th:block th:replace="~{fragments/commonResources :: commonResources}"></th:block>
</head>
<body>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="page-title" th:text="${action}"></h4>
                </div>
                <div class="card-body">
                    <form th:action="@{/albums/save}" method="post" th:object="${album}">
                        <input type="hidden" name="albumId" th:field="*{albumId}"/>
                        <div class="mb-3">
                            <label for="name" class="form-label">相簿名稱 <span class="text-danger">*</span></label>
                            <input type="text"
                                   class="form-control"
                                   id="name"
                                   name="name"
                                   th:field="*{name}"
                                   required
                                   placeholder="相簿名稱">
                        </div>

                        <!--填寫拍攝日期 起訖 -->
                        <div class="mb-3">
                            <label for="shootStart" class="form-label">拍攝起始日期 <span class="text-danger">*</span></label>
                            <input type="date"
                                   class="form-control"
                                   id="shootStart"
                                   name="shootStart"
                                   th:field="*{shootStart}"
                                   required
                                   placeholder="拍攝起始日期">
                        </div>
                        <div class="mb-3">
                            <label for="shootEnd" class="form-label">拍攝結束日期</label>
                            <input type="date"
                                   class="form-control"
                                   id="shootEnd"
                                   name="shootEnd"
                                   th:field="*{shootEnd}"
                                   placeholder="拍攝結束日期">
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a th:href="@{/albums}" class="btn btn-secondary me-md-2">取消</a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span th:text="${album.albumId == null ? '建立相簿' : '更新相簿'}">建立相簿</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>