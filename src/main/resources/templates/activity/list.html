<!DOCTYPE html>
<html  lang="zh-TW"  xmlns:th="http://www.thymeleaf.org">
<head>
    <title>使用者管理 - 活動管理</title>
    <th:block th:replace="~{fragments/commonResources :: commonResources}"></th:block>
</head>
<body>
<div class="page">
    <!-- Sidebar -->
    <div th:replace="~{fragments/sidebar :: sidebar}"></div>

    <!-- Page wrapper -->
    <div class="page-wrapper">
        <div th:replace="~{fragments/message :: message}"></div>
        <!-- Page header -->
        <div class="page-header d-print-none">
            <div class="container-xl">
                <div class="row g-2 align-items-center">
                    <div class="col">
                        <h2 class="page-title">活動管理</h2>
                    </div>
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <a href="/activity/new" class="btn btn-primary d-none d-sm-inline-block">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                新增活動
                            </a>
                            <a href="/activity/new" class="btn btn-primary d-sm-none btn-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page body -->
        <div class="page-body">
            <div class="container-xl">
                <div class="row row-deck row-cards">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">活動列表</h3>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover table-vcenter card-table">
                                    <thead>
                                    <tr>
                                        <th>活動標題</th>
                                        <th>活動說明</th>
                                        <th>建立時間</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr th:each="activity : ${activityPage.content}">
                                        <td th:text="${activity.title}">標題</td>
                                        <td th:text="${activity.summary}">說明</td>
                                        <td th:text="${#temporals.format(activity.createdate, 'yyyy-MM-dd HH:mm')}">建立時間</td>
                                        <td>
                                            <form th:action="@{/activity/{id}/edit(id=${activity.id})}" method="get" style="display: inline;">
                                            <button type="submit" class="btn btn-sm btn-outline-info">
                                                修改
                                            </button></form>
                                            <form th:action="@{/activity/{id}/delete(id=${activity.id})}" method="get" style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('確定要刪除此活動嗎？')">刪除</button>
                                            </form>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <th:block th:replace="~{fragments/pagination :: pagination(${activityPage}, ${size}, ${search}, '/users')}"></th:block>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>