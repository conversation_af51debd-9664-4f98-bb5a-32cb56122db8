<!DOCTYPE html>
<html lang="zh-TW" xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="${action}"></title>
    <th:block th:replace="~{fragments/commonResources :: commonResources}"></th:block>
</head>
<body>
    <div class="container mt-4" x-data="activityForm()">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="page-title" th:text="${action}"></h4>
                        <small class="text-muted" x-show="activityId" x-text="'活動 ID: ' + activityId"></small>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/activity/save}" method="post" th:object="${activity}" >
                            <input type="hidden" name="id" th:field="*{id}" x-model="activityId"/>

                            <div class="mb-3">
                                <label for="title" class="form-label">活動標題 <span class="text-danger">*</span></label>
                                <input type="text"
                                       class="form-control"
                                       id="title"
                                       name="title"
                                       th:field="*{title}"
                                       required
                                       placeholder="標題">
                            </div>

                            <div class="mb-3">
                                <label for="summary" class="form-label">活動說明 <span class="text-danger">*</span></label>
                                <input type="text"
                                       class="form-control"
                                       id="summary"
                                       name="summary"
                                       th:field="*{summary}"
                                       required
                                       placeholder="說明">
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/activity}" class="btn btn-secondary me-md-2">取消</a>
                                <button type="submit"
                                        class="btn btn-primary"
                                        id="submitBtn"
                                        x-text=" (isEditMode ? '更新活動' : '建立活動')">
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        function activityForm() {
            return {
                // 活動資料
                activityId: /*[[${activity.id}]]*/ null,
                title: /*[[${activity.title}]]*/ '',
                summary: /*[[${activity.summary}]]*/ '',
                // 計算屬性
                get isEditMode() {
                    return this.activityId !== null && this.activityId !== undefined;
                },

            }
        }
    </script>
</body>
</html>