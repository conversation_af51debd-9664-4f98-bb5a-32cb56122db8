package com.intfly.photoadmin.repository;

import com.intfly.photoadmin.entity.OcAlbum;
import com.intfly.photoadmin.model.OcAlbumInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.Repository;

import java.util.List;

public interface OcAlbumRepository extends JpaRepository<OcAlbum, Long> {
    Page<OcAlbumInfo> findByNameContainingIgnoreCase(String keyword, Pageable pageable);
    Page<OcAlbumInfo> findByNumberNotNull(Pageable pageable);
}