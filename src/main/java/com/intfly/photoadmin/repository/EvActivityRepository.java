package com.intfly.photoadmin.repository;

import com.intfly.photoadmin.entity.EvActivity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.Repository;

public interface EvActivityRepository extends JpaRepository<EvActivity, Integer> {
    Page<EvActivity> findByTitleContaining(String title, Pageable pageable);
}