package com.intfly.photoadmin.service;

import com.intfly.photoadmin.entity.OcAlbum;
import com.intfly.photoadmin.model.OcAlbumInfo;
import com.intfly.photoadmin.repository.OcAlbumRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class AlbumService {

    private static final Logger log = LoggerFactory.getLogger(AlbumService.class);

    @Autowired
    OcAlbumRepository ocAlbumRepository;

    public Page<OcAlbumInfo> findAllAlbums(Pageable pageable) {
        log.info("查詢所有相簿，分頁參數: {}", pageable);
        return ocAlbumRepository.findByNumberNotNull(pageable);
    }

    public Page<OcAlbumInfo> searchAlbums(String search, Pageable pageable) {
        log.info("搜尋相簿，關鍵字: {}, 分頁參數: {}", search, pageable);
        return ocAlbumRepository.findByNameContainingIgnoreCase(search, pageable);
    }

    public OcAlbum findAlbumById(Long id) {
        log.info("查詢相簿 ID: {}", id);
        return ocAlbumRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("找不到相簿 ID: " + id));
    }

    public OcAlbum createAlbum(OcAlbum ocAlbum) {
        log.info("建立相簿: {}", ocAlbum.getName());

        ocAlbum.setDateAdded(java.time.Instant.now());
        ocAlbum.setDateModified(java.time.Instant.now());
        ocAlbum.setStatus(2);
        OcAlbum savedAlbum = ocAlbumRepository.save(ocAlbum);

        //建立後 還要重新更新一次number 的格式 $albumNumber = "EP".date('Ymd').str_pad($album_id, 3, "0", STR_PAD_LEFT);
        savedAlbum.setNumber("EP" + ocAlbum.getShootStart().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")) + String.format("%03d", ocAlbum.getAlbumId()));

        return ocAlbumRepository.save(savedAlbum);
    }

    public OcAlbum updateAlbum(OcAlbum album) {
        log.info("更新相簿: {}", album.getName());
        
        OcAlbum existingAlbum = findAlbumById(album.getAlbumId());
        existingAlbum.setName(album.getName());
        existingAlbum.setDateModified(java.time.Instant.now());
        
        return ocAlbumRepository.save(existingAlbum);
    }

    public void deleteAlbum(Long id) {
        log.info("刪除相簿 ID: {}", id);
        OcAlbum album = findAlbumById(id);
        ocAlbumRepository.delete(album);
    }
}
