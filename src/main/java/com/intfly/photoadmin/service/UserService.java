
package com.intfly.photoadmin.service;

import com.intfly.photoadmin.entity.Role;
import com.intfly.photoadmin.entity.User;
import com.intfly.photoadmin.repository.RoleRepository;
import com.intfly.photoadmin.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserService {
    
    private static final Logger log = LoggerFactory.getLogger(UserService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RoleService roleService;
    
    // 分頁查詢使用者
    public Page<User> findAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }
    
    // 根據使用者名稱或郵件搜尋（分頁）
    public Page<User> searchUsers(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAllUsers(pageable);
        }
        return userRepository.findByUsernameContainingOrEmailContaining(keyword, keyword, pageable);
    }
    
    // 根據ID查詢使用者
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }
    
    public User createUser(String username, String password, String email, String roleName) {
        log.info("建立新使用者: {}", username);
        
        if (userRepository.existsByUsername(username)) {
            throw new RuntimeException("使用者名稱已存在: " + username);
        }
        
        if (userRepository.existsByEmail(email)) {
            throw new RuntimeException("電子郵件已存在: " + email);
        }
        
        User user = new User(username, passwordEncoder.encode(password), email);
        
        // 設定角色
        user.setRole(roleService.findByName(roleName));
        
        User savedUser = userRepository.save(user);
        log.info("成功建立使用者: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    public List<User> findAllUsers() {
        return userRepository.findAll();
    }
    
    public void deleteUser(Long userId) {
        log.info("刪除使用者 ID: {}", userId);
        userRepository.deleteById(userId);
    }

    public User updateUserWithRole(Long userId, String email, boolean enabled, String newPassword, String roleName) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("找不到使用者 ID: " + userId));

        user.setEmail(email);
        user.setEnabled(enabled);
        user.setUpdatedAt(LocalDateTime.now());
        if (newPassword != null && !newPassword.isEmpty()) {
            user.setPassword(passwordEncoder.encode(newPassword));
        }

        // 設定角色
        user.setRole(roleService.findByName(roleName));
        log.info("成功更新使用者: {}", user.getUsername());

        return userRepository.save(user);
    }
    
    public void changePassword(Long userId, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("找不到使用者 ID: " + userId));
        
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(LocalDateTime.now());
        
        userRepository.save(user);
        log.info("成功更新使用者密碼: {}", user.getUsername());
    }
}
