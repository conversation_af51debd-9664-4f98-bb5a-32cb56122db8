package com.intfly.photoadmin.service;

import com.intfly.photoadmin.entity.EvActivity;
import com.intfly.photoadmin.repository.EvActivityRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.Instant;

import static org.springframework.data.jpa.domain.AbstractPersistable_.id;


@Service
public class ActivityService {

    private static final Logger log = LoggerFactory.getLogger(ActivityService.class);

    @Autowired
    EvActivityRepository evActivityRepository;

    public Page<EvActivity> findAllActivities(Pageable pageable) {
        return evActivityRepository.findAll(pageable);
    }

    public Page<EvActivity> searchActivities(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return findAllActivities(pageable);
        }
        return evActivityRepository.findByTitleContaining(keyword, pageable);
    }

    public EvActivity createActivity(EvActivity evActivity) {
        //資料沒意義 直接給預設值
        evActivity.setActivityseqno("");
        evActivity.setImgurl("");
        evActivity.setPagelink("");
        evActivity.setFlag(0);
        evActivity.setSort(0);
        evActivity.setStartdate(Instant.now());
        evActivity.setEnddate(Instant.now());
        evActivity.setCreatedate(Instant.now());
        evActivity.setModifydate(Instant.now());

        log.info("建立活動: {}", evActivity.getTitle());
        return evActivityRepository.save(evActivity);
    }

    public EvActivity updateActivity(EvActivity evActivity) {
        EvActivity findevActivity = evActivityRepository.findById(evActivity.getId())
                .orElseThrow(() -> new RuntimeException("找不到活動 ID: " + id));

        findevActivity.setModifydate(Instant.now());
        findevActivity.setTitle(evActivity.getTitle());
        findevActivity.setSummary(evActivity.getSummary());

        log.info("更新活動: {}", findevActivity.getTitle());
        return evActivityRepository.save(findevActivity);
    }

    public void deleteActivity(Integer id) {
        log.info("刪除活動 ID: {}", id);
        evActivityRepository.deleteById(id);
    }


    public EvActivity findById(Integer id) {
        log.info("查詢活動 ID: {}", id);
        return evActivityRepository.findById(id).orElseThrow(() -> new RuntimeException("找不到活動 ID: " + id));
    }
}