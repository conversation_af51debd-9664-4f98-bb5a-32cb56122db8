
package com.intfly.photoadmin.service;

import com.intfly.photoadmin.entity.Role;
import com.intfly.photoadmin.repository.RoleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class RoleService {
    
    private static final Logger log = LoggerFactory.getLogger(RoleService.class);
    
    @Autowired
    private RoleRepository roleRepository;
    
    public Role createRole(String name, String description) {
        log.info("建立新角色: {}", name);
        
        if (roleRepository.findByName(name).isPresent()) {
            throw new RuntimeException("角色名稱已存在: " + name);
        }
        
        Role role = new Role(name, description);
        Role savedRole = roleRepository.save(role);
        
        log.info("成功建立角色: {}", savedRole.getName());
        return savedRole;
    }
    
    public List<Role> findAllRoles() {
        return roleRepository.findAll();
    }
    
    public Role findByName(String name) {
        return roleRepository.findByName(name).orElseThrow(() -> new RuntimeException("找不到角色: " + name));
    }
    
    public void deleteRole(Long roleId) {
        log.info("刪除角色 ID: {}", roleId);
        roleRepository.deleteById(roleId);
    }
}
