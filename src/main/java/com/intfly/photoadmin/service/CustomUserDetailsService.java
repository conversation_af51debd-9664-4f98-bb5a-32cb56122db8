package com.intfly.photoadmin.service;

import com.intfly.photoadmin.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class CustomUserDetailsService implements UserDetailsService {
    
    private static final Logger log = LoggerFactory.getLogger(CustomUserDetailsService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.info("嘗試載入使用者: {}", username);
        
        return userRepository.findByUsername(username)
                .orElseThrow(() -> {
                    log.warn("找不到使用者: {}", username);
                    return new UsernameNotFoundException("找不到使用者: " + username);
                });
    }
}