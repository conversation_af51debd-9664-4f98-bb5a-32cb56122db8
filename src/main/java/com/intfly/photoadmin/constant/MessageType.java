
package com.intfly.photoadmin.constant;

public enum MessageType {
    SUCCESS("successMessage", "成功"),
    ERROR("errorMessage", "錯誤"),
    INFO("infoMessage", "資訊"),
    WARNING("warningMessage", "警告");

    private final String code;
    private final String displayName;

    MessageType(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    // 根據 code 取得對應的 MessageType
    public static MessageType fromCode(String code) {
        for (MessageType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的訊息類型代碼: " + code);
    }
}
