package com.intfly.photoadmin.controller;

import com.intfly.photoadmin.constant.MessageType;
import com.intfly.photoadmin.entity.OcAlbum;
import com.intfly.photoadmin.model.OcAlbumInfo;
import com.intfly.photoadmin.service.AlbumService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
@RequestMapping("/albums")
public class AlbumController {
    private static final Logger log = LoggerFactory.getLogger(AlbumController.class);

    @Autowired
    private AlbumService albumService;

    private static final String REDIRECT_ALBUMS = "redirect:/albums";
    private final int size = 10; // 每頁顯示數量

    @GetMapping
    public String listAlbums(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(required = false) String search,
            Model model) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "albumId"));
        
        Page<OcAlbumInfo> albumPage;
        if (search != null && !search.trim().isEmpty()) {
            albumPage = albumService.searchAlbums(search, pageable);
        } else {
            albumPage = albumService.findAllAlbums(pageable);
        }
        
        model.addAttribute("albumPage", albumPage);
        model.addAttribute("currentPage", page);
        model.addAttribute("search", search);
        model.addAttribute("size", size);

        log.info("列出相簿列表 albumPage :{} page: {}, search: {}", albumPage.getContent().size(), page, search);

        return "albums/list";
    }

    @GetMapping("/new")
    public String showCreateForm(Model model) {
        model.addAttribute("album", new OcAlbum());
        model.addAttribute("action", "新增相簿");
        return "albums/form";
    }

    @GetMapping("/{id}/edit")
    public String showEditForm(@PathVariable Long id, Model model) {
        OcAlbum album = albumService.findAlbumById(id);
        model.addAttribute("album", album);
        model.addAttribute("action", "修改相簿");
        return "albums/form";
    }

    @PostMapping("/save")
    public String saveAlbum(OcAlbum album, RedirectAttributes redirectAttributes) {
        log.info("相簿異動新增: {}", album.getName());
        try {
            if (album.getAlbumId() == null) {
                albumService.createAlbum(album);
            } else {
                albumService.updateAlbum(album);
            }
            redirectAttributes.addFlashAttribute(MessageType.SUCCESS.getCode(), "相簿更新成功！");
        } catch (Exception e) {
            log.error("相簿異動失敗: {}", e.getMessage());
            redirectAttributes.addFlashAttribute(MessageType.ERROR.getCode(), "相簿更新失敗！");
        }
        return REDIRECT_ALBUMS;
    }


}
