
package com.intfly.photoadmin.controller;

import com.intfly.photoadmin.constant.MessageType;
import com.intfly.photoadmin.entity.User;
import com.intfly.photoadmin.service.RoleService;
import com.intfly.photoadmin.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
@RequestMapping("/users")
public class UserController {

    private static final Logger log = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RoleService roleService;

    static final int size = 10;
    static final String REDIRECT_USERS = "redirect:/users";

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public String listUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(required = false) String search,
            Model model) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        
        Page<User> userPage;
        if (search != null && !search.trim().isEmpty()) {
            userPage = userService.searchUsers(search, pageable);
        } else {
            userPage = userService.findAllUsers(pageable);
        }
        
        model.addAttribute("userPage", userPage);
        model.addAttribute("currentPage", page);
        model.addAttribute("search", search);
        model.addAttribute("roles", roleService.findAllRoles());

        log.info("列出使用者列表 userPage :{} page: {}, search: {}", userPage.getContent().size(), page, search);

        return "users/list";
    }

    @GetMapping("/new")
    @PreAuthorize("hasRole('ADMIN')")
    public String showCreateForm(Model model) {
        model.addAttribute("user", new User());
        model.addAttribute("roles", roleService.findAllRoles());
        return "users/form";
    }

    @PostMapping("/new")
    @PreAuthorize("hasRole('ADMIN')")
    public String createUser(
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam String email,
            @RequestParam(required = false) String roleName,
            RedirectAttributes redirectAttributes) {
        
        try {
            userService.createUser(username, password, email, roleName);
            redirectAttributes.addFlashAttribute(MessageType.SUCCESS.getCode(), "使用者建立成功！");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute(MessageType.ERROR.getCode(), "建立失敗：" + e.getMessage());
        }
        
        return REDIRECT_USERS;
    }

    @PostMapping("/{id}/edit")
    @PreAuthorize("hasRole('ADMIN')")
    public String updateUser(
            @PathVariable Long id,
            @RequestParam String email,
            @RequestParam(defaultValue = "false") boolean enabled,
            @RequestParam(required = false) String newPassword,
            @RequestParam(required = false) String roleName,
            RedirectAttributes redirectAttributes) {
        
        try {
            if (roleName == null) {
                roleName = "USER"; // 預設角色
            }
            
            userService.updateUserWithRole(id, email, enabled, newPassword, roleName);
            redirectAttributes.addFlashAttribute(MessageType.SUCCESS.getCode(), "使用者更新成功！");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute(MessageType.ERROR.getCode(), "更新失敗：" + e.getMessage());
        }
        
        return REDIRECT_USERS;
    }

    @PostMapping("/{id}/delete")
    @PreAuthorize("hasRole('ADMIN')")
    public String deleteUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            userService.deleteUser(id);
            redirectAttributes.addFlashAttribute(MessageType.SUCCESS.getCode(), "使用者刪除成功！");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute(MessageType.ERROR.getCode(), "刪除失敗：" + e.getMessage());
        }
        
        return REDIRECT_USERS;
    }
}
