package com.intfly.photoadmin.controller;

import com.intfly.photoadmin.constant.MessageType;
import com.intfly.photoadmin.entity.EvActivity;
import com.intfly.photoadmin.service.ActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Controller
@RequestMapping("/activity")
public class ActivityController {

    private static final Logger log = LoggerFactory.getLogger(ActivityController.class);

    static final int size = 10;
    static final String REDIRECT_ACTIVITY = "redirect:/activity";

    @Autowired
    ActivityService activityService;

    @RequestMapping
    public String listActivities(@RequestParam(defaultValue = "0") int page,
                                 @RequestParam(required = false) String search,
                                 Model model) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        model.addAttribute("activityPage", activityService.searchActivities(search ,pageable));
        model.addAttribute("currentPage", page);
        model.addAttribute("search", search);

        return "activity/list";
    }

    @GetMapping("/new")
    public String showCreateForm(Model model) {
        model.addAttribute("activity", new EvActivity());
        model.addAttribute("action", "新增活動");
        return "activity/form";
    }

    @GetMapping("/{id}/edit")
    public String showEditForm(@PathVariable Integer id, Model model) {
        model.addAttribute("activity", activityService.findById(id));
        model.addAttribute("action", "修改活動");
        return "activity/form";
    }

    @GetMapping("/{id}/delete")
    public String deleteActivity(@PathVariable Integer id,RedirectAttributes redirectAttributes) {
        log.info("刪除活動 ID: {}", id);
        activityService.deleteActivity(id);
        redirectAttributes.addFlashAttribute(MessageType.SUCCESS.getCode(), "活動刪除成功！");
        return REDIRECT_ACTIVITY;
    }

    @PostMapping("/save")
    public String  saveActivity(EvActivity evActivity, RedirectAttributes redirectAttributes) {
         log.info("活動異動新增: {}", evActivity.getTitle());
         try {
             if (evActivity.getId() == null) {
                 activityService.createActivity(evActivity);
             } else {
                 activityService.updateActivity(evActivity);
             }
             redirectAttributes.addFlashAttribute(MessageType.SUCCESS.getCode(), "活動更新成功！");
         } catch (Exception e) {
             log.error("活動異動失敗: {}", e.getMessage());
             redirectAttributes.addFlashAttribute(MessageType.ERROR.getCode(), "活動更新失敗！");
         }
      return REDIRECT_ACTIVITY;
    }

}
