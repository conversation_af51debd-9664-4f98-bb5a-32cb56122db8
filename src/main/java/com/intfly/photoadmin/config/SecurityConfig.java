package com.intfly.photoadmin.config;

import com.intfly.photoadmin.service.CustomUserDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    private static final Logger log = LoggerFactory.getLogger(SecurityConfig.class);

    private static final String[] NO_AUTHS_PATHS = {
             "/css/**", "/js/**", "/images/**", "/privacy-policy", "/favicon.ico"
    };
    static final String[] STATIC_RESOURCE_PATHS = {"/webjars/**", "/alpinejs/**", "/htmx/**"};

    private final CustomUserDetailsService userDetailsService;

    public SecurityConfig(CustomUserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }


    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests(requests -> requests
                        .requestMatchers(NO_AUTHS_PATHS).permitAll() // 這些路徑不需要認證
                        .requestMatchers(HttpMethod.GET, STATIC_RESOURCE_PATHS).permitAll()
                        .anyRequest().authenticated() // 其他所有路徑都需要認證
                )
                .formLogin(form -> form
                        .defaultSuccessUrl("/", true)
                        .permitAll()
                )
                .logout(logout -> logout
                        .logoutSuccessUrl("/login")
                        .permitAll()
                ).userDetailsService(userDetailsService);

        log.info("初始化資安路徑配置");
        return http.build();
    }
}
