package com.intfly.photoadmin.model;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Projection for {@link com.intfly.photoadmin.entity.OcAlbum}
 */
public interface OcAlbumInfo {
    Long getAlbumId();

    Integer getActivityseqno();

    String getNumber();

    String getName();

    LocalDate getShootStart();

    LocalDate getShootEnd();

    Integer getCreateUser();

    Integer getModifyUser();

    Integer getStatus();

    Instant getDateAdded();

    Instant getDateModified();
    
    // 新增計算欄位 - 根據 status 值返回對應的狀態名稱
    default String getStatusName() {
        Integer status = getStatus();
        if (status == null) return "未知";

        return switch (status) {
            case 1 -> "上架";
            case 0 -> "刪除";
            case 2 -> "下架";
            default -> "未知";
        };
    }
    
    EvActivityInfo getEvActivity();

    /**
     * Projection for {@link com.intfly.photoadmin.entity.EvActivity}
     */
    interface EvActivityInfo {
        Integer getId();

        String getActivityseqno();

        String getTitle();
    }
}