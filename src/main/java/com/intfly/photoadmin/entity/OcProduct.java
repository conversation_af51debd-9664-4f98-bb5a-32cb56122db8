package com.intfly.photoadmin.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "oc_product")
public class OcProduct {
    @Id
    @Column(name = "product_id", nullable = false)
    private Long productId;

    @Column(name = "album_id", nullable = false)
    private Integer albumId;

    @Column(name = "date_photo", nullable = false)
    private Instant datePhoto;

    @ColumnDefault("0")
    @Column(name = "image_length", nullable = false)
    private Integer imageLength;

    @ColumnDefault("0")
    @Column(name = "image_width", nullable = false)
    private Integer imageWidth;

    @ColumnDefault("0")
    @Column(name = "image_dpi", nullable = false)
    private Integer imageDpi;

    @Column(name = "image_size", length = 20)
    private String imageSize;

    @Column(name = "image_longitude", length = 20)
    private String imageLongitude;

    @Column(name = "image_latitude", length = 20)
    private String imageLatitude;

    @Column(name = "image")
    private String image;

    @Column(name = "photographer", length = 10)
    private String photographer;

    @ColumnDefault("0.0000")
    @Column(name = "price", nullable = false, precision = 15, scale = 4)
    private BigDecimal price;

    @ColumnDefault("0")
    @Column(name = "points", nullable = false)
    private Integer points;

    @Column(name = "create_user", nullable = false)
    private Integer createUser;

    @Column(name = "modify_user", nullable = false)
    private Integer modifyUser;

    @ColumnDefault("1")
    @Column(name = "status", nullable = false)
    private Boolean status = false;

    @Column(name = "date_added", nullable = false)
    private Instant dateAdded;

    @Column(name = "date_modified", nullable = false)
    private Instant dateModified;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getAlbumId() {
        return albumId;
    }

    public void setAlbumId(Integer albumId) {
        this.albumId = albumId;
    }

    public Instant getDatePhoto() {
        return datePhoto;
    }

    public void setDatePhoto(Instant datePhoto) {
        this.datePhoto = datePhoto;
    }

    public Integer getImageLength() {
        return imageLength;
    }

    public void setImageLength(Integer imageLength) {
        this.imageLength = imageLength;
    }

    public Integer getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }

    public Integer getImageDpi() {
        return imageDpi;
    }

    public void setImageDpi(Integer imageDpi) {
        this.imageDpi = imageDpi;
    }

    public String getImageSize() {
        return imageSize;
    }

    public void setImageSize(String imageSize) {
        this.imageSize = imageSize;
    }

    public String getImageLongitude() {
        return imageLongitude;
    }

    public void setImageLongitude(String imageLongitude) {
        this.imageLongitude = imageLongitude;
    }

    public String getImageLatitude() {
        return imageLatitude;
    }

    public void setImageLatitude(String imageLatitude) {
        this.imageLatitude = imageLatitude;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getPhotographer() {
        return photographer;
    }

    public void setPhotographer(String photographer) {
        this.photographer = photographer;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Integer getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(Integer modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Instant getDateAdded() {
        return dateAdded;
    }

    public void setDateAdded(Instant dateAdded) {
        this.dateAdded = dateAdded;
    }

    public Instant getDateModified() {
        return dateModified;
    }

    public void setDateModified(Instant dateModified) {
        this.dateModified = dateModified;
    }

}