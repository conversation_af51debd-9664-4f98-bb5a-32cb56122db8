package com.intfly.photoadmin.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class OcCategoryDescriptionId implements Serializable {
    private static final long serialVersionUID = -4535407101384305159L;
    @Column(name = "category_id", nullable = false)
    private Integer categoryId;

    @Column(name = "language_id", nullable = false)
    private Integer languageId;

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        OcCategoryDescriptionId entity = (OcCategoryDescriptionId) o;
        return Objects.equals(this.languageId, entity.languageId) &&
                Objects.equals(this.categoryId, entity.categoryId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(languageId, categoryId);
    }

}