package com.intfly.photoadmin.entity;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "oc_product_to_category")
public class OcProductToCategory {
    @EmbeddedId
    private OcProductToCategoryId id;

    public OcProductToCategoryId getId() {
        return id;
    }

    public void setId(OcProductToCategoryId id) {
        this.id = id;
    }

    //TODO [Reverse Engineering] generate columns from DB
}