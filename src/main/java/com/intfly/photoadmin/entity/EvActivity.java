package com.intfly.photoadmin.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.Instant;

@Entity
@Table(name = "ev_activity")
public class EvActivity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seqno", nullable = false)
    private Integer id;

    @Column(name = "activityseqno", length = 36)
    private String activityseqno;

    @Column(name = "title", nullable = false, length = 100)
    private String title;

    @Column(name = "summary", nullable = false, length = 300)
    private String summary;

    @Column(name = "imgurl", nullable = false, length = 256)
    private String imgurl;

    @Column(name = "pagelink", nullable = false, length = 256)
    private String pagelink;

    @ColumnDefault("0")
    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "sort", nullable = false)
    private Integer sort;

    @Column(name = "startdate", nullable = false)
    private Instant startdate;

    @Column(name = "enddate", nullable = false)
    private Instant enddate;

    @Column(name = "createdate", nullable = false)
    private Instant createdate;

    @Column(name = "modifydate", nullable = false)
    private Instant modifydate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityseqno() {
        return activityseqno;
    }

    public void setActivityseqno(String activityseqno) {
        this.activityseqno = activityseqno;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getImgurl() {
        return imgurl;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl;
    }

    public String getPagelink() {
        return pagelink;
    }

    public void setPagelink(String pagelink) {
        this.pagelink = pagelink;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Instant getStartdate() {
        return startdate;
    }

    public void setStartdate(Instant startdate) {
        this.startdate = startdate;
    }

    public Instant getEnddate() {
        return enddate;
    }

    public void setEnddate(Instant enddate) {
        this.enddate = enddate;
    }

    public Instant getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Instant createdate) {
        this.createdate = createdate;
    }

    public Instant getModifydate() {
        return modifydate;
    }

    public void setModifydate(Instant modifydate) {
        this.modifydate = modifydate;
    }

}