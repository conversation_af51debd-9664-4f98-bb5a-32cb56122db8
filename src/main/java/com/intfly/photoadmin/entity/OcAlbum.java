package com.intfly.photoadmin.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.Instant;
import java.time.LocalDate;

@Entity
@Table(name = "oc_album")
public class OcAlbum {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "album_id", nullable = false)
    private Long albumId;

    @Column(name = "activityseqno", nullable = false)
    private Integer activityseqno;

    @Column(name = "photog_id", nullable = false)
    private Integer photogId;

    @Column(name = "number", nullable = false, length = 30)
    private String number;

    @Column(name = "name", nullable = false, length = 150)
    private String name;

    @Lob
    @Column(name = "tag")
    private String tag;

    @Column(name = "cover_id")
    private Integer coverId;

    @Column(name = "shoot_start", nullable = false)
    private LocalDate shootStart;

    @Column(name = "shoot_end")
    private LocalDate shootEnd;

    @Lob
    @Column(name = "buylink")
    private String buylink;

    @Column(name = "create_user", nullable = false)
    private Integer createUser;

    @Column(name = "modify_user", nullable = false)
    private Integer modifyUser;

    @ColumnDefault("1")
    @Column(name = "status", nullable = false)
    private Integer status;

    @Column(name = "date_added", nullable = false)
    private Instant dateAdded;

    @Column(name = "date_modified", nullable = false)
    private Instant dateModified;

    @OneToOne
    @JoinColumn(name = "activityseqno", unique = true, insertable = false, updatable = false)
    private EvActivity evActivity;

    public EvActivity getEvActivity() {
        return evActivity;
    }

    public void setEvActivity(EvActivity evActivity) {
        this.evActivity = evActivity;
    }

    public Long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(Long albumId) {
        this.albumId = albumId;
    }

    public Integer getActivityseqno() {
        return activityseqno;
    }

    public void setActivityseqno(Integer activityseqno) {
        this.activityseqno = activityseqno;
    }

    public Integer getPhotogId() {
        return photogId;
    }

    public void setPhotogId(Integer photogId) {
        this.photogId = photogId;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getCoverId() {
        return coverId;
    }

    public void setCoverId(Integer coverId) {
        this.coverId = coverId;
    }

    public LocalDate getShootStart() {
        return shootStart;
    }

    public void setShootStart(LocalDate shootStart) {
        this.shootStart = shootStart;
    }

    public LocalDate getShootEnd() {
        return shootEnd;
    }

    public void setShootEnd(LocalDate shootEnd) {
        this.shootEnd = shootEnd;
    }

    public String getBuylink() {
        return buylink;
    }

    public void setBuylink(String buylink) {
        this.buylink = buylink;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Integer getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(Integer modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Instant getDateAdded() {
        return dateAdded;
    }

    public void setDateAdded(Instant dateAdded) {
        this.dateAdded = dateAdded;
    }

    public Instant getDateModified() {
        return dateModified;
    }

    public void setDateModified(Instant dateModified) {
        this.dateModified = dateModified;
    }

}