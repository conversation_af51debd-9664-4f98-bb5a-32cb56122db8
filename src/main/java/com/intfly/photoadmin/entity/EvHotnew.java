package com.intfly.photoadmin.entity;

import jakarta.persistence.*;

import java.time.Instant;

@Entity
@Table(name = "ev_hotnews")
public class EvHotnew {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seqno", nullable = false)
    private Long id;

    @Column(name = "activityseqno", length = 36)
    private String activityseqno;

    @Column(name = "newsdate")
    private Instant newsdate;

    @Column(name = "title", length = 100)
    private String title;

    @Column(name = "summary", length = 350)
    private String summary;

    @Lob
    @Column(name = "content")
    private String content;

    @Column(name = "publishstartdate")
    private Instant publishstartdate;

    @Column(name = "publishenddate")
    private Instant publishenddate;

    @Column(name = "createtime")
    private Instant createtime;

    @Column(name = "creator", length = 100)
    private String creator;

    @Column(name = "modifytime")
    private Instant modifytime;

    @Column(name = "modifier", length = 100)
    private String modifier;

    @Column(name = "usergroupseqno", length = 36)
    private String usergroupseqno;

    @Column(name = "imgurl", length = 256)
    private String imgurl;

    @Column(name = "shareimgurl", length = 256)
    private String shareimgurl;

    @Column(name = "data_source", length = 20)
    private String dataSource;

    @Column(name = "source_url", length = 256)
    private String sourceUrl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityseqno() {
        return activityseqno;
    }

    public void setActivityseqno(String activityseqno) {
        this.activityseqno = activityseqno;
    }

    public Instant getNewsdate() {
        return newsdate;
    }

    public void setNewsdate(Instant newsdate) {
        this.newsdate = newsdate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Instant getPublishstartdate() {
        return publishstartdate;
    }

    public void setPublishstartdate(Instant publishstartdate) {
        this.publishstartdate = publishstartdate;
    }

    public Instant getPublishenddate() {
        return publishenddate;
    }

    public void setPublishenddate(Instant publishenddate) {
        this.publishenddate = publishenddate;
    }

    public Instant getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Instant createtime) {
        this.createtime = createtime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Instant getModifytime() {
        return modifytime;
    }

    public void setModifytime(Instant modifytime) {
        this.modifytime = modifytime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getUsergroupseqno() {
        return usergroupseqno;
    }

    public void setUsergroupseqno(String usergroupseqno) {
        this.usergroupseqno = usergroupseqno;
    }

    public String getImgurl() {
        return imgurl;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl;
    }

    public String getShareimgurl() {
        return shareimgurl;
    }

    public void setShareimgurl(String shareimgurl) {
        this.shareimgurl = shareimgurl;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

}