package com.intfly.photoadmin.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "oc_category_description")
public class OcCategoryDescription {
    @EmbeddedId
    private OcCategoryDescriptionId id;

    @Column(name = "name", nullable = false)
    private String name;

    @Lob
    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "meta_title", nullable = false)
    private String metaTitle;

    @Column(name = "meta_description", nullable = false)
    private String metaDescription;

    @Column(name = "meta_keyword", nullable = false)
    private String metaKeyword;

    public OcCategoryDescriptionId getId() {
        return id;
    }

    public void setId(OcCategoryDescriptionId id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMetaTitle() {
        return metaTitle;
    }

    public void setMetaTitle(String metaTitle) {
        this.metaTitle = metaTitle;
    }

    public String getMetaDescription() {
        return metaDescription;
    }

    public void setMetaDescription(String metaDescription) {
        this.metaDescription = metaDescription;
    }

    public String getMetaKeyword() {
        return metaKeyword;
    }

    public void setMetaKeyword(String metaKeyword) {
        this.metaKeyword = metaKeyword;
    }

}