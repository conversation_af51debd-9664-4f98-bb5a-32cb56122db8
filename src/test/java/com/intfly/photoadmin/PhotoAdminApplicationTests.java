package com.intfly.photoadmin;

import com.intfly.photoadmin.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class PhotoAdminApplicationTests {
    @Autowired
    private UserService userService;

    @Test
    void contextLoads() {
        try{
            userService.createUser("testuser", "test1234", "<EMAIL>", "USER");
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
